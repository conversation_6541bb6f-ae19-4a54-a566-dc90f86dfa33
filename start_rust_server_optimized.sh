#!/bin/bash

# Optimized Rust Server Startup Script
# Reduces memory usage and allocation pressure

RUST_DIR="$HOME/rust_server"
SERVER_EXE="$RUST_DIR/RustDedicated"
SERVER_IDENTITY="my_server_identity"

echo "=========================================="
echo "   Starting Optimized Rust Server"
echo "=========================================="
echo

# Check if server executable exists
if [[ ! -f "$SERVER_EXE" ]]; then
    echo "❌ Error: RustDedicated not found at $SERVER_EXE"
    exit 1
fi

# Change to server directory
cd "$RUST_DIR"

echo "Starting server with memory optimizations..."
echo "Server Identity: $SERVER_IDENTITY"
echo "Reduced world size: 2000"
echo "Reduced max players: 50"
echo

# Start with memory optimizations
"$SERVER_EXE" -batchmode -nographics \
    +server.identity "$SERVER_IDENTITY" \
    -force-gfx-jobs \
    -force-clamped \
    -logfile ~/rust_server_optimized.log

echo
echo "Server has stopped."
