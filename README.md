# Rust Server Installer & Auto-Updater

A comprehensive bash script for installing, updating, and managing Rust dedicated servers on Linux with support for Oxide (uMod) and Carbon mod frameworks.

## Features

- 🚀 **Automated Installation**: One-click setup of Rust dedicated server
- 🔄 **Auto-Updates**: Keeps your server and mods up to date
- 🛠️ **Mod Support**: Choose between Oxide (uMod) or Carbon frameworks
- 📦 **Dependency Management**: Automatically installs required packages
- 🐧 **Linux Optimized**: Designed specifically for Linux servers

## Prerequisites

- Ubuntu/Debian-based Linux distribution
- `sudo` privileges for dependency installation
- Internet connection for downloads

## Installation

### 1. Download the Script

```bash
# Clone or download the script
wget https://raw.githubusercontent.com/your-repo/rust-server-installer/main/install_rust_server.sh
chmod +x install_rust_server.sh
```

### 2. Run the Installer

```bash
./install_rust_server.sh
```

The script will:
1. Install required dependencies (`lib32gcc-s1`, `curl`, `wget`, `tar`, `unzip`, `screen`)
2. Download and install SteamCMD
3. Download and install Rust dedicated server (~9.5GB)
4. Prompt you to choose a mod framework:
   - **Option 1**: Oxide (uMod) - Traditional modding framework
   - **Option 2**: Carbon - High-performance modern framework

### 3. Installation Locations

- **Rust Server**: `~/rust_server/`
- **SteamCMD**: `~/steamcmd/`
- **Server Executable**: `~/rust_server/RustDedicated`
- **Server Config**: `~/rust_server/server/my_server_identity/server.cfg` (auto-created)

## Server Management

### Starting the Server

#### Basic Start (Foreground)
```bash
cd ~/rust_server
./RustDedicated -batchmode -nographics
```

#### Start with Screen (Background)
```bash
cd ~/rust_server
screen -S rust-server ./RustDedicated -batchmode -nographics \
  +server.hostname "My Rust Server" \
  +server.port 28015 \
  +server.maxplayers 100 \
  +server.worldsize 3000 \
  +server.seed 12345 \
  +server.saveinterval 300
```

#### Start with Custom Configuration
```bash
cd ~/rust_server
screen -S rust-server ./RustDedicated -batchmode -nographics \
  +server.hostname "Your Server Name" \
  +server.port 28015 \
  +server.maxplayers 50 \
  +server.worldsize 4000 \
  +server.seed 54321 \
  +server.saveinterval 600 \
  +rcon.port 28016 \
  +rcon.password "your_rcon_password" \
  +rcon.web 1
```

### Managing Screen Sessions

#### View Running Sessions
```bash
screen -ls
```

#### Attach to Server Console
```bash
screen -r rust-server
```

#### Detach from Console
Press `Ctrl+A` then `D` to detach without stopping the server

### Stopping the Server

#### Graceful Shutdown (from server console)
1. Attach to the screen session: `screen -r rust-server`
2. Type: `quit` or `save` then `quit`
3. Press `Ctrl+A` then `D` to detach

#### Force Stop
```bash
# Find the process
ps aux | grep RustDedicated

# Kill the process (replace XXXX with actual PID)
kill XXXX

# Or kill the screen session
screen -S rust-server -X quit
```

### Restarting the Server

#### Method 1: Stop and Start
```bash
# Stop the server
screen -S rust-server -X quit

# Wait a moment, then start again
cd ~/rust_server
screen -S rust-server ./RustDedicated -batchmode -nographics [your_parameters]
```

#### Method 2: Restart Script
Create a restart script:

```bash
nano ~/restart_server.sh
```

Add this content:
```bash
#!/bin/bash
echo "Stopping Rust server..."
screen -S rust-server -X quit
sleep 5

echo "Starting Rust server..."
cd ~/rust_server
screen -S rust-server -dm ./RustDedicated -batchmode -nographics \
  +server.hostname "My Rust Server" \
  +server.port 28015 \
  +server.maxplayers 100 \
  +server.worldsize 3000 \
  +server.seed 12345 \
  +server.saveinterval 300

echo "Server restarted!"
```

Make it executable:
```bash
chmod +x ~/restart_server.sh
```

## Server Configuration

### Using server.cfg (Recommended)

The installer automatically creates a `server.cfg` file in your server directory with default settings. This is the easiest way to configure your server:

```bash
# Edit the configuration file
nano ~/rust_server/server/my_server_identity/server.cfg
```

Key settings you can modify:
```cfg
server.hostname "Your Server Name"
server.port 28015
server.maxplayers 100
server.worldsize 3000
server.seed 12345
rcon.password "your_secure_password"
```

After editing, simply start your server and it will use these settings automatically.

### Command Line Parameters (Alternative)

If you prefer command line parameters, here are the important ones:

| Parameter | Description | Example |
|-----------|-------------|---------|
| `+server.hostname` | Server name | `"My Rust Server"` |
| `+server.port` | Game port | `28015` |
| `+server.maxplayers` | Max players | `100` |
| `+server.worldsize` | Map size | `3000` (1000-4000) |
| `+server.seed` | World seed | `12345` |
| `+server.saveinterval` | Save frequency (seconds) | `300` |
| `+rcon.port` | RCON port | `28016` |
| `+rcon.password` | RCON password | `"secure_password"` |
| `+rcon.web` | Enable web RCON | `1` |

### Network Ports

Make sure these ports are open in your firewall:
- **28015** (UDP) - Game port
- **28016** (TCP) - RCON port (if enabled)

```bash
# Ubuntu/Debian firewall rules
sudo ufw allow 28015/udp
sudo ufw allow 28016/tcp
```

## Updating

### Update Server and Mods
Simply run the installer script again:
```bash
./install_rust_server.sh
```

This will:
- Update the Rust server to the latest version
- Update your chosen mod framework
- Preserve your existing configuration

### Update Only the Server
```bash
cd ~/steamcmd
./steamcmd.sh +login anonymous +force_install_dir ~/rust_server +app_update 258550 validate +quit
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x install_rust_server.sh
   ```

2. **Missing Dependencies**
   ```bash
   sudo apt-get update
   sudo apt-get install lib32gcc-s1 curl wget tar unzip screen
   ```

3. **Server Won't Start**
   - Check if ports are available: `netstat -tulpn | grep 28015`
   - Verify file permissions: `ls -la ~/rust_server/RustDedicated`
   - Check server logs in the screen session

4. **Can't Connect to Server**
   - Verify firewall settings
   - Check if server is running: `screen -ls`
   - Confirm port configuration

### Log Files

Server logs are displayed in the screen session. To save logs to a file:
```bash
screen -S rust-server -L ./RustDedicated [parameters]
```

## Support

- **Rust Server**: [Facepunch Studios](https://rust.facepunch.com/)
- **Oxide/uMod**: [uMod.org](https://umod.org/)
- **Carbon**: [CarbonMod.gg](https://carbonmod.gg/)

## License

This script is provided as-is for educational and server management purposes.
