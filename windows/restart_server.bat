@echo off
setlocal

REM Rust Server Restart Script for Windows

echo ========================================
echo     Restarting Rust Dedicated Server
echo ========================================
echo.

echo Step 1: Stopping existing server...
call stop_server.bat

echo.
echo Step 2: Waiting for complete shutdown...
timeout /t 5 /nobreak >nul

echo.
echo Step 3: Starting server in background...
call start_server_background.bat

echo.
echo ✅ Server restart complete!
echo.
pause
