@echo off
setlocal

REM Rust Server Background Startup Script for Windows
REM This starts the server in a new window that can be minimized

set "RUST_DIR=%USERPROFILE%\rust_server"
set "SERVER_EXE=%RUST_DIR%\RustDedicated.exe"

REM Server Configuration - Modify these values as needed
set "SERVER_HOSTNAME=My Rust Server"
set "SERVER_PORT=28015"
set "SERVER_MAXPLAYERS=100"
set "SERVER_WORLDSIZE=3000"
set "SERVER_SEED=12345"
set "SERVER_SAVEINTERVAL=300"
set "RCON_PORT=28016"
set "RCON_PASSWORD=change_this_password"
set "RCON_WEB=1"

echo ========================================
echo   Starting Rust Server in Background
echo ========================================
echo.
echo Server: %SERVER_HOSTNAME%
echo Port: %SERVER_PORT%
echo Max Players: %SERVER_MAXPLAYERS%
echo World Size: %SERVER_WORLDSIZE%
echo Seed: %SERVER_SEED%
echo.

REM Check if server executable exists
if not exist "%SERVER_EXE%" (
    echo ❌ Error: RustDedicated.exe not found at %SERVER_EXE%
    echo Please run install_rust_server.bat first to install the server.
    echo.
    pause
    exit /b 1
)

REM Check if server is already running
tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠️  Warning: RustDedicated.exe is already running!
    echo Please stop the existing server before starting a new one.
    echo.
    pause
    exit /b 1
)

echo Starting server in background window...
echo You can minimize the server window that opens.
echo Use stop_server.bat to stop the server.
echo.

REM Start the server in a new window
start "Rust Dedicated Server" /D "%RUST_DIR%" "%SERVER_EXE%" -batchmode -nographics ^
    +server.hostname "%SERVER_HOSTNAME%" ^
    +server.port %SERVER_PORT% ^
    +server.maxplayers %SERVER_MAXPLAYERS% ^
    +server.worldsize %SERVER_WORLDSIZE% ^
    +server.seed %SERVER_SEED% ^
    +server.saveinterval %SERVER_SAVEINTERVAL% ^
    +rcon.port %RCON_PORT% ^
    +rcon.password "%RCON_PASSWORD%" ^
    +rcon.web %RCON_WEB%

echo ✅ Server started in background!
echo.
echo To stop the server, run: stop_server.bat
echo To restart the server, run: restart_server.bat
echo.
pause
