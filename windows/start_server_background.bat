@echo off
setlocal

REM Rust Server Background Startup Script for Windows
REM This starts the server in a new window that can be minimized

set "RUST_DIR=%USERPROFILE%\rust_server"
set "SERVER_EXE=%RUST_DIR%\RustDedicated.exe"
set "SERVER_IDENTITY=my_server_identity"
set "CONFIG_FILE=%RUST_DIR%\server\%SERVER_IDENTITY%\server.cfg"

echo ========================================
echo   Starting Rust Server in Background
echo ========================================
echo.

REM Check if config file exists
if exist "%CONFIG_FILE%" (
    echo Using configuration file: %CONFIG_FILE%
    echo Edit server.cfg to customize server settings.
) else (
    echo Warning: server.cfg not found. Using default settings.
    echo Create %CONFIG_FILE% to customize your server.
)
echo.

REM Check if server executable exists
if not exist "%SERVER_EXE%" (
    echo ❌ Error: RustDedicated.exe not found at %SERVER_EXE%
    echo Please run install_rust_server.bat first to install the server.
    echo.
    pause
    exit /b 1
)

REM Check if server is already running
tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠️  Warning: RustDedicated.exe is already running!
    echo Please stop the existing server before starting a new one.
    echo.
    pause
    exit /b 1
)

echo Starting server in background window...
echo You can minimize the server window that opens.
echo Use stop_server.bat to stop the server.
echo.

REM Start the server in a new window
if exist "%CONFIG_FILE%" (
    start "Rust Dedicated Server" /D "%RUST_DIR%" "%SERVER_EXE%" -batchmode -nographics +server.identity "%SERVER_IDENTITY%"
) else (
    start "Rust Dedicated Server" /D "%RUST_DIR%" "%SERVER_EXE%" -batchmode -nographics ^
        +server.identity "%SERVER_IDENTITY%" ^
        +server.hostname "My Rust Server" ^
        +server.port 28015 ^
        +server.maxplayers 100 ^
        +server.worldsize 3000 ^
        +server.saveinterval 300
)

echo ✅ Server started in background!
echo.
echo To stop the server, run: stop_server.bat
echo To restart the server, run: restart_server.bat
echo.
pause
