@echo off
setlocal enabledelayedexpansion

REM Rust server install & auto-update script with Carbon or Oxide support for Windows

set "RUST_DIR=%USERPROFILE%\rust_server"
set "STEAMCMD_DIR=%USERPROFILE%\steamcmd"
set "STEAMCMD_URL=https://steamcdn-a.akamaihd.net/client/installer/steamcmd.zip"

echo ========================================
echo    Rust Server Installer for Windows
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Warning: Not running as administrator. Some features may not work properly.
    echo Please run as administrator for best results.
    echo.
    pause
)

REM Check for required tools
where curl >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: curl is not installed or not in PATH.
    echo Please install curl or use Windows 10/11 which includes it by default.
    pause
    exit /b 1
)

where powershell >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: PowerShell is not available.
    echo This script requires PowerShell to function properly.
    pause
    exit /b 1
)

call :install_steamcmd
call :install_or_update_rust
call :ask_mod_choice

echo.
echo ✅ Server installation and update complete.
echo Server files located at: %RUST_DIR%
echo.
echo To start your server, run: start_server.bat
echo.
pause
exit /b 0

:install_steamcmd
echo Installing SteamCMD...
if not exist "%STEAMCMD_DIR%" mkdir "%STEAMCMD_DIR%"
cd /d "%STEAMCMD_DIR%"

if not exist "steamcmd.exe" (
    echo Downloading SteamCMD...
    curl -L "%STEAMCMD_URL%" -o steamcmd.zip
    if %errorLevel% neq 0 (
        echo ❌ Failed to download SteamCMD
        pause
        exit /b 1
    )
    
    echo Extracting SteamCMD...
    powershell -command "Expand-Archive -Path 'steamcmd.zip' -DestinationPath '.' -Force"
    del steamcmd.zip
    echo ✅ SteamCMD installed successfully
) else (
    echo ✅ SteamCMD already installed
)
echo.
goto :eof

:install_or_update_rust
echo Installing or updating Rust server...
if not exist "%RUST_DIR%" mkdir "%RUST_DIR%"

echo Running SteamCMD to download/update Rust server...
"%STEAMCMD_DIR%\steamcmd.exe" +login anonymous +force_install_dir "%RUST_DIR%" +app_update 258550 validate +quit

if %errorLevel% neq 0 (
    echo ❌ Failed to install/update Rust server
    pause
    exit /b 1
)
echo ✅ Rust server installed/updated successfully
echo.
goto :eof

:install_or_update_oxide
echo Installing or updating Oxide (uMod)...
cd /d "%RUST_DIR%"

echo Fetching latest Oxide download URL...
for /f "tokens=*" %%i in ('curl -s "https://api.github.com/repos/OxideMod/Oxide.Rust/releases/latest" ^| findstr "browser_download_url.*Oxide\.Rust\.zip"') do set "OXIDE_LINE=%%i"

REM Extract URL from the line
for /f "tokens=2 delims=:" %%a in ("!OXIDE_LINE!") do (
    for /f "tokens=1 delims=," %%b in ("%%a") do (
        set "OXIDE_URL=%%b"
        set "OXIDE_URL=!OXIDE_URL: =!"
        set "OXIDE_URL=!OXIDE_URL:"=!"
        set "OXIDE_URL=https:!OXIDE_URL!"
    )
)

if "!OXIDE_URL!"=="" (
    echo ❌ Failed to find Oxide download URL from GitHub API.
    echo Please visit https://github.com/OxideMod/Oxide.Rust/releases to download manually.
    goto :eof
)

echo Downloading Oxide from: !OXIDE_URL!
curl -L "!OXIDE_URL!" -o oxide.zip
if %errorLevel% neq 0 (
    echo ❌ Download failed.
    goto :eof
)

if exist oxide.zip (
    echo Extracting Oxide...
    powershell -command "Expand-Archive -Path 'oxide.zip' -DestinationPath '.' -Force"
    del oxide.zip
    echo ✅ Oxide installed/updated.
) else (
    echo ❌ Oxide download failed.
)
echo.
goto :eof

:install_or_update_carbon
echo Installing or updating Carbon...
cd /d "%RUST_DIR%"

echo Fetching latest Carbon download URL...
for /f "tokens=*" %%i in ('curl -s "https://api.github.com/repos/CarbonCommunity/Carbon/releases/latest" ^| findstr "browser_download_url.*Carbon\.Windows\.Release\.zip"') do set "CARBON_LINE=%%i"

REM Extract URL from the line
for /f "tokens=2 delims=:" %%a in ("!CARBON_LINE!") do (
    for /f "tokens=1 delims=," %%b in ("%%a") do (
        set "CARBON_URL=%%b"
        set "CARBON_URL=!CARBON_URL: =!"
        set "CARBON_URL=!CARBON_URL:"=!"
        set "CARBON_URL=https:!CARBON_URL!"
    )
)

if "!CARBON_URL!"=="" (
    echo ❌ Failed to find a Carbon download URL from GitHub API.
    echo Please visit https://github.com/CarbonCommunity/Carbon/releases to download manually.
    goto :eof
)

echo Downloading Carbon from: !CARBON_URL!
curl -L "!CARBON_URL!" -o carbon.zip
if %errorLevel% neq 0 (
    echo ❌ Download failed.
    goto :eof
)

if exist carbon.zip (
    echo Extracting Carbon...
    powershell -command "Expand-Archive -Path 'carbon.zip' -DestinationPath '.' -Force"
    del carbon.zip
    echo ✅ Carbon installed/updated.
) else (
    echo ❌ Carbon download failed.
)
echo.
goto :eof

:ask_mod_choice
echo Choose server mod to install or update:
echo 1) Oxide (uMod)
echo 2) Carbon
echo 3) Skip mod installation
echo.
set /p mod_choice="Enter choice [1-3]: "

if "%mod_choice%"=="1" (
    call :install_or_update_oxide
) else if "%mod_choice%"=="2" (
    call :install_or_update_carbon
) else if "%mod_choice%"=="3" (
    echo Skipping mod installation.
) else (
    echo Invalid choice. Skipping mod installation.
)
goto :eof
