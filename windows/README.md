# Rust Server Installer & Manager for Windows

A comprehensive set of batch scripts for installing, updating, and managing Rust dedicated servers on Windows with support for Oxide (uMod) and Carbon mod frameworks.

## Features

- 🚀 **Automated Installation**: One-click setup of Rust dedicated server
- 🔄 **Auto-Updates**: Keeps your server and mods up to date
- 🛠️ **Mod Support**: Choose between Oxide (uMod) or Carbon frameworks
- 📦 **Dependency Management**: Automatically downloads required tools
- 🪟 **Windows Optimized**: Designed specifically for Windows 10/11

## Prerequisites

- Windows 10 or Windows 11
- Administrator privileges (recommended)
- Internet connection for downloads
- Windows Defender Firewall configured (see Network Configuration)

## Installation

### 1. Download the Scripts

Download all the `.bat` files to a folder on your computer (e.g., `C:\RustServer\`):
- `install_rust_server.bat`
- `start_server.bat`
- `start_server_background.bat`
- `stop_server.bat`
- `restart_server.bat`
- `update_server.bat`

### 2. Run the Installer

1. **Right-click** on `install_rust_server.bat`
2. Select **"Run as administrator"** (recommended)
3. Follow the prompts

The script will:
1. Check for required tools (curl, PowerShell)
2. Download and install SteamCMD
3. Download and install Rust dedicated server (~9.5GB)
4. Prompt you to choose a mod framework:
   - **Option 1**: Oxide (uMod) - Traditional modding framework
   - **Option 2**: Carbon - High-performance modern framework
   - **Option 3**: Skip mod installation

### 3. Installation Locations

- **Rust Server**: `%USERPROFILE%\rust_server\`
- **SteamCMD**: `%USERPROFILE%\steamcmd\`
- **Server Executable**: `%USERPROFILE%\rust_server\RustDedicated.exe`
- **Server Config**: `%USERPROFILE%\rust_server\server\my_server_identity\server.cfg` (auto-created)

## Server Management

### Starting the Server

#### Option 1: Foreground Mode
```batch
start_server.bat
```
- Runs in the current command window
- Shows server console output
- Press `Ctrl+C` to stop

#### Option 2: Background Mode (Recommended)
```batch
start_server_background.bat
```
- Opens server in a new window
- Can be minimized
- Use `stop_server.bat` to stop

### Stopping the Server

```batch
stop_server.bat
```
- Attempts graceful shutdown first
- Forces shutdown if necessary
- Confirms server has stopped

### Restarting the Server

```batch
restart_server.bat
```
- Stops the current server
- Waits for complete shutdown
- Starts server in background mode

### Updating Server and Mods

```batch
update_server.bat
```
- Checks if server is running and offers to stop it
- Updates Rust server to latest version
- Optionally updates mod frameworks
- Offers to start server after update

## Server Configuration

### Using server.cfg (Recommended)

The installer automatically creates a `server.cfg` file in your server directory with default settings. This is the easiest way to configure your server:

1. Navigate to `%USERPROFILE%\rust_server\server\my_server_identity\`
2. Open `server.cfg` in Notepad or your preferred text editor
3. Modify the settings as needed
4. Save the file
5. Start your server - it will automatically use these settings

Key settings you can modify:
```cfg
server.hostname "Your Server Name"
server.port 28015
server.maxplayers 100
server.worldsize 3000
server.seed 12345
rcon.password "your_secure_password"
```

### Legacy Method: Editing Batch Files

Alternatively, you can edit the start scripts directly. Open `start_server.bat` or `start_server_background.bat` in a text editor and modify these variables:

```batch
set "SERVER_HOSTNAME=My Rust Server"
set "SERVER_PORT=28015"
set "SERVER_MAXPLAYERS=100"
set "SERVER_WORLDSIZE=3000"
set "SERVER_SEED=12345"
set "SERVER_SAVEINTERVAL=300"
set "RCON_PORT=28016"
set "RCON_PASSWORD=change_this_password"
set "RCON_WEB=1"
```

### Server Parameters Reference

| Parameter | Description | Default | Range/Options |
|-----------|-------------|---------|---------------|
| `SERVER_HOSTNAME` | Server name in browser | "My Rust Server" | Any string |
| `SERVER_PORT` | Game port | 28015 | 1024-65535 |
| `SERVER_MAXPLAYERS` | Maximum players | 100 | 1-500+ |
| `SERVER_WORLDSIZE` | Map size | 3000 | 1000-4000 |
| `SERVER_SEED` | World generation seed | 12345 | Any number |
| `SERVER_SAVEINTERVAL` | Save frequency (seconds) | 300 | 60-3600 |
| `RCON_PORT` | Remote console port | 28016 | 1024-65535 |
| `RCON_PASSWORD` | RCON password | "change_this_password" | Any string |
| `RCON_WEB` | Enable web RCON | 1 | 0 or 1 |

## Network Configuration

### Windows Firewall

The server requires these ports to be open:

#### Automatic Configuration (Run as Administrator)
```batch
netsh advfirewall firewall add rule name="Rust Server Game Port" dir=in action=allow protocol=UDP localport=28015
netsh advfirewall firewall add rule name="Rust Server RCON Port" dir=in action=allow protocol=TCP localport=28016
```

#### Manual Configuration
1. Open **Windows Defender Firewall with Advanced Security**
2. Click **Inbound Rules** → **New Rule**
3. Select **Port** → **Next**
4. Select **UDP** → **Specific local ports** → Enter `28015` → **Next**
5. Select **Allow the connection** → **Next**
6. Check all profiles → **Next**
7. Name: "Rust Server Game Port" → **Finish**
8. Repeat for TCP port 28016 (RCON)

### Router Port Forwarding

Forward these ports to your server computer:
- **28015 UDP** (Game port)
- **28016 TCP** (RCON port, if using remote admin tools)

## File Structure

```
windows/
├── install_rust_server.bat      # Main installer script
├── start_server.bat             # Start server (foreground)
├── start_server_background.bat  # Start server (background)
├── stop_server.bat              # Stop server gracefully
├── restart_server.bat           # Restart server
├── update_server.bat            # Update server and mods
└── README.md                    # This file

%USERPROFILE%/
├── rust_server/                 # Rust server files
│   ├── RustDedicated.exe        # Server executable
│   ├── RustDedicated_Data/      # Game data
│   └── [mod files]              # Oxide/Carbon files
└── steamcmd/                    # SteamCMD installation
    └── steamcmd.exe             # SteamCMD executable
```

## Troubleshooting

### Common Issues

#### 1. "curl is not recognized"
- **Windows 10/11**: curl is included by default
- **Older Windows**: Download curl from https://curl.se/windows/
- **Alternative**: Use PowerShell version of the script

#### 2. "Access is denied" or Permission Errors
- Right-click script and select "Run as administrator"
- Check Windows Defender isn't blocking the files
- Ensure user has write permissions to the installation directory

#### 3. Server Won't Start
- Check if ports 28015/28016 are already in use:
  ```batch
  netstat -an | findstr :28015
  ```
- Verify firewall settings
- Check if `RustDedicated.exe` exists in the rust_server folder

#### 4. Can't Connect to Server
- Verify Windows Firewall rules are created
- Check router port forwarding
- Confirm server is running: `tasklist | findstr RustDedicated`
- Test locally first: connect to `localhost:28015`

#### 5. High CPU/Memory Usage
- Reduce `SERVER_MAXPLAYERS`
- Decrease `SERVER_WORLDSIZE`
- Close unnecessary programs
- Consider upgrading hardware

### Server Logs

Server output is displayed in the console window. To save logs to a file, modify the start command:

```batch
"%SERVER_EXE%" [parameters] > server_log.txt 2>&1
```

### Performance Optimization

#### For Better Performance:
- Set Windows power plan to "High Performance"
- Close unnecessary background applications
- Increase virtual memory (pagefile) size
- Use SSD storage for server files
- Ensure adequate RAM (8GB+ recommended)

#### Server Settings for Performance:
```batch
set "SERVER_MAXPLAYERS=50"        # Reduce for better performance
set "SERVER_WORLDSIZE=2000"       # Smaller world = better performance
set "SERVER_SAVEINTERVAL=600"     # Less frequent saves
```

## Advanced Usage

### Running Multiple Servers

To run multiple servers, copy the scripts to different folders and modify:
- `SERVER_PORT` (e.g., 28015, 28025, 28035)
- `RCON_PORT` (e.g., 28016, 28026, 28036)
- `SERVER_HOSTNAME`

### Scheduled Restarts

Use Windows Task Scheduler to automatically restart the server:
1. Open **Task Scheduler**
2. Create **Basic Task**
3. Set trigger (e.g., daily at 6 AM)
4. Action: **Start a program**
5. Program: `C:\path\to\restart_server.bat`

### Backup Scripts

Create a backup script to save server data:
```batch
@echo off
set "BACKUP_DIR=C:\RustBackups\%date:~-4,4%-%date:~-10,2%-%date:~-7,2%"
mkdir "%BACKUP_DIR%"
xcopy "%USERPROFILE%\rust_server\server" "%BACKUP_DIR%" /E /I /Y
echo Backup completed to %BACKUP_DIR%
```

## Support and Resources

- **Rust Server**: [Facepunch Studios](https://rust.facepunch.com/)
- **Oxide/uMod**: [uMod.org](https://umod.org/)
- **Carbon**: [CarbonMod.gg](https://carbonmod.gg/)
- **SteamCMD**: [Valve Developer Wiki](https://developer.valvesoftware.com/wiki/SteamCMD)

## License

These scripts are provided as-is for educational and server management purposes.
