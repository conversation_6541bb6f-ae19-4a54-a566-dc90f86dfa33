@echo off
setlocal

REM Rust Server Startup Script for Windows
REM Customize the server parameters below

set "RUST_DIR=%USERPROFILE%\rust_server"
set "SERVER_EXE=%RUST_DIR%\RustDedicated.exe"
set "SERVER_IDENTITY=my_server_identity"
set "CONFIG_FILE=%RUST_DIR%\server\%SERVER_IDENTITY%\server.cfg"

echo ========================================
echo      Starting Rust Dedicated Server
echo ========================================
echo.

REM Check if config file exists
if exist "%CONFIG_FILE%" (
    echo Using configuration file: %CONFIG_FILE%
    echo Edit server.cfg to customize server settings.
) else (
    echo Warning: server.cfg not found. Using default settings.
    echo Create %CONFIG_FILE% to customize your server.
)
echo.

REM Check if server executable exists
if not exist "%SERVER_EXE%" (
    echo ❌ Error: RustDedicated.exe not found at %SERVER_EXE%
    echo Please run install_rust_server.bat first to install the server.
    echo.
    pause
    exit /b 1
)

REM Change to server directory
cd /d "%RUST_DIR%"

REM Start the server
echo Starting server...
echo Press Ctrl+C to stop the server
echo.

if exist "%CONFIG_FILE%" (
    "%SERVER_EXE%" -batchmode -nographics +server.identity "%SERVER_IDENTITY%"
) else (
    "%SERVER_EXE%" -batchmode -nographics ^
        +server.identity "%SERVER_IDENTITY%" ^
        +server.hostname "My Rust Server" ^
        +server.port 28015 ^
        +server.maxplayers 100 ^
        +server.worldsize 3000 ^
        +server.saveinterval 300
)

echo.
echo Server has stopped.
pause
