@echo off
setlocal

REM Rust Server Startup Script for Windows
REM Customize the server parameters below

set "RUST_DIR=%USERPROFILE%\rust_server"
set "SERVER_EXE=%RUST_DIR%\RustDedicated.exe"

REM Server Configuration - Modify these values as needed
set "SERVER_HOSTNAME=My Rust Server"
set "SERVER_PORT=28015"
set "SERVER_MAXPLAYERS=100"
set "SERVER_WORLDSIZE=3000"
set "SERVER_SEED=12345"
set "SERVER_SAVEINTERVAL=300"
set "RCON_PORT=28016"
set "RCON_PASSWORD=change_this_password"
set "RCON_WEB=1"

echo ========================================
echo      Starting Rust Dedicated Server
echo ========================================
echo.
echo Server: %SERVER_HOSTNAME%
echo Port: %SERVER_PORT%
echo Max Players: %SERVER_MAXPLAYERS%
echo World Size: %SERVER_WORLDSIZE%
echo Seed: %SERVER_SEED%
echo.

REM Check if server executable exists
if not exist "%SERVER_EXE%" (
    echo ❌ Error: RustDedicated.exe not found at %SERVER_EXE%
    echo Please run install_rust_server.bat first to install the server.
    echo.
    pause
    exit /b 1
)

REM Change to server directory
cd /d "%RUST_DIR%"

REM Start the server with configuration
echo Starting server...
echo Press Ctrl+C to stop the server
echo.

"%SERVER_EXE%" -batchmode -nographics ^
    +server.hostname "%SERVER_HOSTNAME%" ^
    +server.port %SERVER_PORT% ^
    +server.maxplayers %SERVER_MAXPLAYERS% ^
    +server.worldsize %SERVER_WORLDSIZE% ^
    +server.seed %SERVER_SEED% ^
    +server.saveinterval %SERVER_SAVEINTERVAL% ^
    +rcon.port %RCON_PORT% ^
    +rcon.password "%RCON_PASSWORD%" ^
    +rcon.web %RCON_WEB%

echo.
echo Server has stopped.
pause
