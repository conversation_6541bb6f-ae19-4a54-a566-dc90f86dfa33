@echo off
setlocal

REM Rust Server Update Script for Windows

set "RUST_DIR=%USERPROFILE%\rust_server"
set "STEAMCMD_DIR=%USERPROFILE%\steamcmd"

echo ========================================
echo      Updating Rust Dedicated Server
echo ========================================
echo.

REM Check if server is running
tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠️  Warning: Rust server is currently running!
    echo.
    set /p choice="Do you want to stop the server and continue with update? (y/n): "
    if /i "!choice!"=="y" (
        echo Stopping server...
        call stop_server.bat
        echo.
        echo Waiting for server to fully stop...
        timeout /t 5 /nobreak >nul
    ) else (
        echo Update cancelled. Please stop the server manually and try again.
        pause
        exit /b 1
    )
)

echo Checking for SteamCMD...
if not exist "%STEAMCMD_DIR%\steamcmd.exe" (
    echo ❌ SteamCMD not found. Please run install_rust_server.bat first.
    pause
    exit /b 1
)

echo ✅ SteamCMD found. Starting update...
echo.

REM Update the server
echo Updating Rust server files...
echo This may take several minutes depending on the update size...
echo.

cd /d "%STEAMCMD_DIR%"
steamcmd.exe +login anonymous +force_install_dir "%RUST_DIR%" +app_update 258550 validate +quit

if %errorLevel% neq 0 (
    echo ❌ Failed to update Rust server
    pause
    exit /b 1
)

echo.
echo ✅ Rust server updated successfully!
echo.

REM Ask if user wants to update mods
echo Do you want to update your mod framework as well?
echo 1) Update Oxide (uMod)
echo 2) Update Carbon
echo 3) Skip mod update
echo.
set /p mod_choice="Enter choice [1-3]: "

if "%mod_choice%"=="1" (
    echo.
    echo Updating Oxide...
    call :update_oxide
) else if "%mod_choice%"=="2" (
    echo.
    echo Updating Carbon...
    call :update_carbon
) else (
    echo Skipping mod update.
)

echo.
echo ✅ Update process complete!
echo.
set /p start_choice="Do you want to start the server now? (y/n): "
if /i "%start_choice%"=="y" (
    call start_server_background.bat
)

pause
exit /b 0

:update_oxide
cd /d "%RUST_DIR%"

echo Fetching latest Oxide download URL...
for /f "tokens=*" %%i in ('curl -s "https://api.github.com/repos/OxideMod/Oxide.Rust/releases/latest" ^| findstr "browser_download_url.*Oxide\.Rust\.zip"') do set "OXIDE_LINE=%%i"

REM Extract URL from the line
for /f "tokens=2 delims=:" %%a in ("!OXIDE_LINE!") do (
    for /f "tokens=1 delims=," %%b in ("%%a") do (
        set "OXIDE_URL=%%b"
        set "OXIDE_URL=!OXIDE_URL: =!"
        set "OXIDE_URL=!OXIDE_URL:"=!"
        set "OXIDE_URL=https:!OXIDE_URL!"
    )
)

if "!OXIDE_URL!"=="" (
    echo ❌ Failed to find Oxide download URL
    goto :eof
)

echo Downloading latest Oxide...
curl -L "!OXIDE_URL!" -o oxide.zip
if %errorLevel% neq 0 (
    echo ❌ Download failed
    goto :eof
)

echo Extracting Oxide...
powershell -command "Expand-Archive -Path 'oxide.zip' -DestinationPath '.' -Force"
del oxide.zip
echo ✅ Oxide updated successfully
goto :eof

:update_carbon
cd /d "%RUST_DIR%"

echo Fetching latest Carbon download URL...
for /f "tokens=*" %%i in ('curl -s "https://api.github.com/repos/CarbonCommunity/Carbon/releases/latest" ^| findstr "browser_download_url.*Carbon\.Windows\.Release\.zip"') do set "CARBON_LINE=%%i"

REM Extract URL from the line
for /f "tokens=2 delims=:" %%a in ("!CARBON_LINE!") do (
    for /f "tokens=1 delims=," %%b in ("%%a") do (
        set "CARBON_URL=%%b"
        set "CARBON_URL=!CARBON_URL: =!"
        set "CARBON_URL=!CARBON_URL:"=!"
        set "CARBON_URL=https:!CARBON_URL!"
    )
)

if "!CARBON_URL!"=="" (
    echo ❌ Failed to find Carbon download URL
    goto :eof
)

echo Downloading latest Carbon...
curl -L "!CARBON_URL!" -o carbon.zip
if %errorLevel% neq 0 (
    echo ❌ Download failed
    goto :eof
)

echo Extracting Carbon...
powershell -command "Expand-Archive -Path 'carbon.zip' -DestinationPath '.' -Force"
del carbon.zip
echo ✅ Carbon updated successfully
goto :eof
