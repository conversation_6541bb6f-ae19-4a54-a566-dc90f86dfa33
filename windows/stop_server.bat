@echo off
setlocal

REM Rust Server Stop Script for Windows

echo ========================================
echo      Stopping Rust Dedicated Server
echo ========================================
echo.

REM Check if server is running
tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo ℹ️  No Rust server is currently running.
    echo.
    pause
    exit /b 0
)

echo Found running Rust server process(es):
tasklist /FI "IMAGENAME eq RustDedicated.exe"
echo.

echo Attempting graceful shutdown...
echo This may take a few moments as the server saves data...
echo.

REM Try graceful shutdown first (this sends a close signal)
taskkill /IM "RustDedicated.exe" /T >nul 2>&1

REM Wait a bit for graceful shutdown
timeout /t 10 /nobreak >nul

REM Check if still running
tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Server is still running, forcing shutdown...
    taskkill /F /IM "RustDedicated.exe" /T >nul 2>&1
    
    REM Final check
    tasklist /FI "IMAGENAME eq RustDedicated.exe" 2>NUL | find /I /N "RustDedicated.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo ❌ Failed to stop the server. Please check Task Manager.
        pause
        exit /b 1
    ) else (
        echo ✅ Server forcefully stopped.
    )
) else (
    echo ✅ Server stopped gracefully.
)

echo.
echo Server has been stopped successfully.
echo.
pause
