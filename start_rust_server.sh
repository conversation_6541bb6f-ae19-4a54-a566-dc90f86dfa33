#!/bin/bash

# Rust Server Startup Script for Linux
# This script starts the Rust server using server.cfg for configuration

RUST_DIR="$HOME/rust_server"
SERVER_EXE="$RUST_DIR/RustDedicated"
CONFIG_FILE="$RUST_DIR/server.cfg"

echo "=========================================="
echo "      Starting Rust Dedicated Server"
echo "=========================================="
echo

# Check if server executable exists
if [[ ! -f "$SERVER_EXE" ]]; then
    echo "❌ Error: RustDedicated not found at $SERVER_EXE"
    echo "Please run install_rust_server.sh first to install the server."
    exit 1
fi

# Check if config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "⚠️  Warning: server.cfg not found at $CONFIG_FILE"
    echo "The server will start with default settings."
    echo "Create a server.cfg file to customize your server configuration."
    echo
fi

# Change to server directory
cd "$RUST_DIR"

echo "Server directory: $RUST_DIR"
if [[ -f "$CONFIG_FILE" ]]; then
    echo "Using config file: $CONFIG_FILE"
    echo "Server settings:"
    echo "  Hostname: $(grep 'server.hostname' "$CONFIG_FILE" 2>/dev/null | cut -d'"' -f2 || echo 'Default')"
    echo "  Port: $(grep 'server.port' "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' || echo '28015')"
    echo "  Max Players: $(grep 'server.maxplayers' "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' || echo '100')"
    echo "  World Size: $(grep 'server.worldsize' "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' || echo '3000')"
fi
echo
echo "Starting server..."
echo "Press Ctrl+C to stop the server"
echo

# Start the server
if [[ -f "$CONFIG_FILE" ]]; then
    # Start with config file
    "$SERVER_EXE" -batchmode -nographics +server.cfg "$CONFIG_FILE"
else
    # Start with minimal default settings
    "$SERVER_EXE" -batchmode -nographics \
        +server.hostname "Rust Server" \
        +server.port 28015 \
        +server.maxplayers 100 \
        +server.worldsize 3000 \
        +server.saveinterval 300
fi

echo
echo "Server has stopped."
