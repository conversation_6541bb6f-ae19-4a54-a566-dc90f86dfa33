Rust Server Installer with Carbon or Oxide Mod Support
====================================================

📦 Features:
- Installs SteamCMD and Rust Dedicated Server
- Lets you choose between Oxide (uMod) or Carbon mod
- Supports auto-updates for the server and mod

📁 Contents:
- install_rust_server.sh — Main installer and updater script

🛠️ Installation Instructions:

1. Extract the ZIP file:
   unzip rust_server_installer.zip
   cd rust_server_installer

2. Make the script executable:
   chmod +x install_rust_server.sh

3. Run the installer:
   ./install_rust_server.sh

   You’ll be prompted to choose between Carbon and Oxide.

▶️ Starting the Server:
   cd ~/rust_server
   ./RustDedicated -batchmode +server.port 28015 +server.level "Procedural Map" +server.hostname "My Rust Server" +server.identity "my_server" +rcon.port 28016 +rcon.password "changeme" +rcon.web 1

⏹️ Stopping the Server:
- Press `Ctrl+C` in the terminal running the server.
- Or use `kill $(pidof RustDedicated)`.

🔁 Restarting the Server:
- Stop the server (as above) and then re-run the start command.

🔄 Updating Server and Mods:
- Just rerun the installer script:
   ./install_rust_server.sh
- It will update both the Rust server and your selected mod.

📌 Notes:
- Make sure ports 28015 (game) and 28016 (RCON) are open in your firewall.
- Carbon and Oxide are not compatible. Install only one.

Enjoy your Rust server!
