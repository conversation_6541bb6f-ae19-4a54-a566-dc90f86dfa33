#!/bin/bash

# Rust Server Monitor Script

echo "🔍 Monitoring Rust Server..."
echo "Press Ctrl+C to stop monitoring"
echo "=================================="

while true; do
    clear
    echo "🔍 Rust Server Monitor - $(date)"
    echo "=================================="
    
    # Check if server process is running
    if pgrep -f "frank.*RustDedicated" > /dev/null; then
        PROCESS_INFO=$(ps aux | grep frank | grep RustDedicated | grep -v grep)
        PID=$(echo "$PROCESS_INFO" | awk '{print $2}')
        CPU=$(echo "$PROCESS_INFO" | awk '{print $3}')
        MEM=$(echo "$PROCESS_INFO" | awk '{print $4}')
        
        echo "✅ Server Status: RUNNING"
        echo "   PID: $PID"
        echo "   CPU: ${CPU}%"
        echo "   Memory: ${MEM}%"
        
        # Check ports
        if netstat -tulpn 2>/dev/null | grep -q ":28015"; then
            echo "✅ Game Port: LISTENING"
        else
            echo "❌ Game Port: NOT LISTENING"
        fi
        
    else
        echo "❌ Server Status: STOPPED"
        echo ""
        echo "📝 Last 5 lines from debug log:"
        tail -5 ~/rust_server_debug.log 2>/dev/null || echo "   No log file found"
        echo ""
        echo "📝 Last 5 lines from screen log:"
        tail -5 ~/rust_server_screen.log 2>/dev/null || echo "   No screen log found"
        break
    fi
    
    echo ""
    echo "📊 Recent Log Activity:"
    echo "Debug log size: $(ls -lh ~/rust_server_debug.log 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    echo "Screen log size: $(ls -lh ~/rust_server_screen.log 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    
    echo ""
    echo "📝 Last 3 debug log lines:"
    tail -3 ~/rust_server_debug.log 2>/dev/null | sed 's/^/   /' || echo "   No recent logs"
    
    echo ""
    echo "⏱️  Next check in 30 seconds... (Ctrl+C to stop)"
    
    sleep 30
done

echo ""
echo "🚨 Server has stopped! Check the logs above for details."
