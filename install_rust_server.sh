#!/bin/bash

# Rust server install & auto-update script with Carbon or Oxide support

RUST_DIR="$HOME/rust_server"
STEAMCMD_DIR="$HOME/steamcmd"
STEAMCMD_URL="https://steamcdn-a.akamaihd.net/client/installer/steamcmd_linux.tar.gz"

function install_dependencies() {
    echo "Installing dependencies..."
    sudo apt-get update
    sudo apt-get install -y lib32gcc-s1 curl wget tar unzip screen
}

function install_steamcmd() {
    echo "Installing SteamCMD..."
    mkdir -p "$STEAMCMD_DIR"
    cd "$STEAMCMD_DIR"
    wget "$STEAMCMD_URL" -O steamcmd_linux.tar.gz
    tar -xvzf steamcmd_linux.tar.gz
}

function install_or_update_rust() {
    echo "Installing or updating Rust server..."
    mkdir -p "$RUST_DIR"
    "$STEAMCMD_DIR/steamcmd.sh" +login anonymous +force_install_dir "$RUST_DIR" +app_update 258550 validate +quit
}

function install_or_update_oxide() {
    echo "Installing or updating Oxide (uMod)..."
    mkdir -p "$RUST_DIR"
    cd "$RUST_DIR"
    OXIDE_URL=$(curl -s https://umod.org/games/rust | grep -Eo 'https://[^"]+\.zip' | head -n 1)

    if [[ -z "$OXIDE_URL" ]]; then
        echo "❌ Failed to find Oxide download URL."
        return 1
    fi

    echo "Downloading Oxide from: $OXIDE_URL"
    wget "$OXIDE_URL" -O oxide.zip || { echo "❌ Download failed."; return 1; }

    if [[ -f oxide.zip ]]; then
        unzip -o oxide.zip && rm oxide.zip
        echo "✅ Oxide installed/updated."
    else
        echo "❌ Oxide download failed."
        return 1
    fi
}

function install_or_update_carbon() {
    echo "Installing or updating Carbon..."
    mkdir -p "$RUST_DIR"
    cd "$RUST_DIR"

    # Try to get Carbon from the official API
    CARBON_URL=$(curl -s https://api.carbonmod.gg/api/v1/releases/latest | grep -Eo 'https://.*\.zip' | head -n 1)

    if [[ -z "$CARBON_URL" ]]; then
        echo "❌ Failed to find a Carbon download URL from API."
        echo "Please visit https://carbonmod.gg/ to download manually."
        return 1
    fi

    echo "Downloading Carbon from: $CARBON_URL"
    wget "$CARBON_URL" -O carbon.zip || { echo "❌ Download failed."; return 1; }

    if [[ -f carbon.zip ]]; then
        unzip -o carbon.zip && rm carbon.zip
        echo "✅ Carbon installed/updated."
    else
        echo "❌ Carbon download failed."
        return 1
    fi
}

function ask_mod_choice() {
    echo "Choose server mod to install or update:"
    echo "1) Oxide (uMod)"
    echo "2) Carbon"
    read -p "Enter choice [1-2]: " mod_choice
    case $mod_choice in
        1) install_or_update_oxide ;;
        2) install_or_update_carbon ;;
        *) echo "Invalid choice." ;;
    esac
}

echo "Rust Server Installer & Auto-Updater"
install_dependencies
install_steamcmd
install_or_update_rust
ask_mod_choice

echo "✅ Server installation and update complete."
echo "Server files located at: $RUST_DIR"
