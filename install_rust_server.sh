#!/bin/bash

# Rust server install & auto-update script with Carbon or Oxide support

RUST_DIR="$HOME/rust_server"
STEAMCMD_DIR="$HOME/steamcmd"
STEAMCMD_URL="https://steamcdn-a.akamaihd.net/client/installer/steamcmd_linux.tar.gz"

function install_dependencies() {
    echo "Installing dependencies..."
    sudo apt-get update
    sudo apt-get install -y lib32gcc-s1 curl wget tar unzip screen
}

function install_steamcmd() {
    echo "Installing SteamCMD..."
    mkdir -p "$STEAMCMD_DIR"
    cd "$STEAMCMD_DIR"
    wget "$STEAMCMD_URL" -O steamcmd_linux.tar.gz
    tar -xvzf steamcmd_linux.tar.gz
}

function install_or_update_rust() {
    echo "Installing or updating Rust server..."
    mkdir -p "$RUST_DIR"
    "$STEAMCMD_DIR/steamcmd.sh" +login anonymous +force_install_dir "$RUST_DIR" +app_update 258550 validate +quit
}

function install_or_update_oxide() {
    echo "Installing or updating Oxide (uMod)..."
    cd "$RUST_DIR"
    OXIDE_URL=$(curl -s https://umod.org/games/rust | grep -Eo 'https://[^"]+\.zip' | head -n 1)
    wget "$OXIDE_URL" -O oxide.zip
    unzip -o oxide.zip
    rm oxide.zip
}

function install_or_update_carbon() {
  echo "Installing/updating Carbon..."
  cd "$RUST_DIR"
  # Fetch latest Linux ZIP download URL from GitHub
  API_URL="https://codefling.com/carbon/carbon?do=download&r=472149&confirm=1&t=1&csrfKey=ff68faa8301f8c540db33cb5899922ba"
  DOWNLOAD_URL=$(curl -s "$API_URL" |
    grep '"browser_download_url":' |
    grep 'linux' |
    grep '\.zip"' |
    head -n1 |
    cut -d '"' -f4)
  if [[ -z "$DOWNLOAD_URL" ]]; then
    echo "❌ Failed to find a Carbon Linux download URL."
    return 1
  fi
  echo "Downloading Carbon from: $DOWNLOAD_URL"
  wget -q "$DOWNLOAD_URL" -O carbon.zip || { echo "❌ Download failed."; return 1; }
  unzip -o carbon.zip && rm carbon.zip
  echo "✅ Carbon installed/updated."
}
    echo "Installing or updating Carbon..."
    cd "$RUST_DIR"
    CARBON_URL=$(curl -s https://api.carbonmod.gg/api/v1/releases/latest | grep -Eo 'https://.*\.zip' | head -n 1)
    wget "$CARBON_URL" -O carbon.zip
    unzip -o carbon.zip
    rm carbon.zip
}

function ask_mod_choice() {
    echo "Choose server mod to install or update:"
    echo "1) Oxide (uMod)"
    echo "2) Carbon"
    read -p "Enter choice [1-2]: " mod_choice
    case $mod_choice in
        1) install_or_update_oxide ;;
        2) install_or_update_carbon ;;
        *) echo "Invalid choice." ;;
    esac
}

echo "Rust Server Installer & Auto-Updater"
install_dependencies
install_steamcmd
install_or_update_rust
ask_mod_choice

echo "✅ Server installation and update complete."
echo "Server files located at: $RUST_DIR"
