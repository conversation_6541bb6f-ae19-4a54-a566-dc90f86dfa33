#!/bin/bash

# Rust server install & auto-update script with Carbon or Oxide support

RUST_DIR="$HOME/rust_server"
STEAMCMD_DIR="$HOME/steamcmd"
STEAMCMD_URL="https://steamcdn-a.akamaihd.net/client/installer/steamcmd_linux.tar.gz"

function install_dependencies() {
    echo "Installing dependencies..."
    sudo apt-get update
    sudo apt-get install -y lib32gcc-s1 curl wget tar unzip screen
}

function install_steamcmd() {
    echo "Installing SteamCMD..."
    mkdir -p "$STEAMCMD_DIR"
    cd "$STEAMCMD_DIR"
    wget "$STEAMCMD_URL" -O steamcmd_linux.tar.gz
    tar -xvzf steamcmd_linux.tar.gz
}

function install_or_update_rust() {
    echo "Installing or updating Rust server..."
    mkdir -p "$RUST_DIR"
    "$STEAMCMD_DIR/steamcmd.sh" +login anonymous +force_install_dir "$RUST_DIR" +app_update 258550 validate +quit
}

function install_or_update_oxide() {
    echo "Installing or updating Oxide (uMod)..."
    mkdir -p "$RUST_DIR"
    cd "$RUST_DIR"

    # Get Oxide from the official GitHub API
    OXIDE_URL=$(curl -s https://api.github.com/repos/OxideMod/Oxide.Rust/releases/latest | grep -o '"browser_download_url": *"[^"]*Oxide\.Rust-linux\.zip"' | grep -o 'https://[^"]*')

    if [[ -z "$OXIDE_URL" ]]; then
        echo "❌ Failed to find Oxide download URL from GitHub API."
        echo "Please visit https://github.com/OxideMod/Oxide.Rust/releases to download manually."
        return 1
    fi

    echo "Downloading Oxide from: $OXIDE_URL"
    wget "$OXIDE_URL" -O oxide.zip || { echo "❌ Download failed."; return 1; }

    if [[ -f oxide.zip ]]; then
        unzip -o oxide.zip && rm oxide.zip
        echo "✅ Oxide installed/updated."
    else
        echo "❌ Oxide download failed."
        return 1
    fi
}

function install_or_update_carbon() {
    echo "Installing or updating Carbon..."
    mkdir -p "$RUST_DIR"
    cd "$RUST_DIR"

    # Get Carbon from the official GitHub API
    CARBON_URL=$(curl -s https://api.github.com/repos/CarbonCommunity/Carbon/releases/latest | grep -o '"browser_download_url": *"[^"]*Carbon\.Linux\.Release\.tar\.gz"' | grep -o 'https://[^"]*')

    if [[ -z "$CARBON_URL" ]]; then
        echo "❌ Failed to find a Carbon download URL from GitHub API."
        echo "Please visit https://github.com/CarbonCommunity/Carbon/releases to download manually."
        return 1
    fi

    echo "Downloading Carbon from: $CARBON_URL"
    wget "$CARBON_URL" -O carbon.tar.gz || { echo "❌ Download failed."; return 1; }

    if [[ -f carbon.tar.gz ]]; then
        tar -xzf carbon.tar.gz && rm carbon.tar.gz
        echo "✅ Carbon installed/updated."
    else
        echo "❌ Carbon download failed."
        return 1
    fi
}

function ask_mod_choice() {
    echo "Choose server mod to install or update:"
    echo "1) Oxide (uMod)"
    echo "2) Carbon"
    read -p "Enter choice [1-2]: " mod_choice
    case $mod_choice in
        1) install_or_update_oxide ;;
        2) install_or_update_carbon ;;
        *) echo "Invalid choice." ;;
    esac
}

echo "Rust Server Installer & Auto-Updater"
install_dependencies
install_steamcmd
install_or_update_rust
ask_mod_choice

echo "✅ Server installation and update complete."
echo "Server files located at: $RUST_DIR"

# Create server identity directory and server.cfg
SERVER_IDENTITY_DIR="$RUST_DIR/server/my_server_identity"
mkdir -p "$SERVER_IDENTITY_DIR"

if [[ ! -f "$SERVER_IDENTITY_DIR/server.cfg" ]]; then
    echo "Creating default server.cfg in server identity directory..."
    cat > "$SERVER_IDENTITY_DIR/server.cfg" << 'EOF'
# Rust Server Configuration File
# Edit these values to customize your server

# Network Configuration
server.port 28015
server.queryport 28016
app.port 28003

# Server Identity
server.hostname "My Rust Server"
server.description "Welcome to our Rust server! Have fun and follow the rules."
server.url "https://your-website.com"
server.headerimage "https://your-website.com/header.jpg"

# World Settings
server.level "Procedural Map"
server.seed 12345
server.worldsize 3000
server.maxplayers 100

# Team Settings
maxteamsize 4

# Performance Settings
server.saveinterval 300
fps.limit 60

# Chat and Logging
chat.serverlog true
lang en

# Gameplay Settings
instant.craft "false"

# Media Settings
BoomBox.ServerUrlList "https://www.youtube.com,https://soundcloud.com,https://open.spotify.com"

# Server Tags (helps players find your server)
server.tags "vanilla,friendly,active,weekly"

# RCON Settings (Remote Console)
rcon.port 28017
rcon.password "change_this_rcon_password"
rcon.web 1

# Additional Server Settings
server.stability true
server.radiation true
decay.scale 1.0
craft.instant false

# Anti-Cheat Settings
antihack.enabled true
antihack.reporting true
antihack.admincheat false

# Comfort Settings
comfort true

# PVP Settings (set to false for PVE server)
server.pve false

# Voice Chat
voice.enabled true

# Server Browser Settings
server.secure true

# Networking
network.sendrate 20
network.updaterate 20

# Console Settings
server.tickrate 30

# Debugging (set to false for production)
debug.log false
EOF
    echo "✅ Default server.cfg created at $SERVER_IDENTITY_DIR/server.cfg"
    echo "📝 Edit this file to customize your server settings before starting."
fi
