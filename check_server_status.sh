#!/bin/bash

# Rust Server Status Checker

echo "=========================================="
echo "        Rust Server Status Check"
echo "=========================================="
echo

# Check if server process is running
if pgrep -f "RustDedicated" > /dev/null; then
    echo "✅ Server Process: RUNNING"
    
    # Get process details
    PROCESS_INFO=$(ps aux | grep -v grep | grep RustDedicated)
    PID=$(echo "$PROCESS_INFO" | awk '{print $2}')
    CPU=$(echo "$PROCESS_INFO" | awk '{print $3}')
    MEM=$(echo "$PROCESS_INFO" | awk '{print $4}')
    
    echo "   PID: $PID"
    echo "   CPU Usage: ${CPU}%"
    echo "   Memory Usage: ${MEM}%"
else
    echo "❌ Server Process: NOT RUNNING"
fi

echo

# Check if game port is listening
if netstat -tulpn 2>/dev/null | grep -q ":28015"; then
    echo "✅ Game Port (28015): LISTENING"
else
    echo "❌ Game Port (28015): NOT LISTENING"
fi

# Check if RCON port is listening
if netstat -tulpn 2>/dev/null | grep -q ":28017"; then
    echo "✅ RCON Port (28017): LISTENING"
else
    echo "❌ RCON Port (28017): NOT LISTENING"
fi

echo

# Check server files
if [[ -f "$HOME/rust_server/RustDedicated" ]]; then
    echo "✅ Server Executable: FOUND"
else
    echo "❌ Server Executable: NOT FOUND"
fi

CONFIG_FILE="$HOME/rust_server/server/my_server_identity/server.cfg"
if [[ -f "$CONFIG_FILE" ]]; then
    echo "✅ Server Config: FOUND"
    echo "   Location: $CONFIG_FILE"

    # Show key config values
    echo "   Server Name: $(grep 'server.hostname' "$CONFIG_FILE" 2>/dev/null | cut -d'"' -f2 || echo 'Not set')"
    echo "   Max Players: $(grep 'server.maxplayers' "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' || echo 'Not set')"
    echo "   World Size: $(grep 'server.worldsize' "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' || echo 'Not set')"
else
    echo "❌ Server Config: NOT FOUND"
    echo "   Expected location: $CONFIG_FILE"
fi

echo

# Check screen sessions
if command -v screen >/dev/null 2>&1; then
    SCREEN_SESSIONS=$(screen -ls 2>/dev/null | grep rust-server | wc -l)
    if [[ $SCREEN_SESSIONS -gt 0 ]]; then
        echo "✅ Screen Session: ACTIVE"
        echo "   Use 'screen -r rust-server' to attach"
    else
        echo "ℹ️  Screen Session: NONE"
    fi
fi

echo
echo "=========================================="

# Show recent log entries if available
if [[ -f "$HOME/rust_server/output_log.txt" ]]; then
    echo "📝 Recent Log Entries:"
    tail -n 5 "$HOME/rust_server/output_log.txt" 2>/dev/null || echo "   No recent logs available"
fi

echo
