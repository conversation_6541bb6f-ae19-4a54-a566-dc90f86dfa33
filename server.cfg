# Rust Server Configuration File
# Place this file in your rust_server directory
# Customize the values below for your server

# Network Configuration
server.port 28015
server.queryport 28016
app.port 28003

# Server Identity
server.hostname "My Rust Server"
server.description "Welcome to our Rust server! Have fun and follow the rules."
server.url "https://your-website.com"
server.headerimage "https://your-website.com/header.jpg"

# World Settings
server.level "Procedural Map"
server.seed 12345
server.worldsize 3000
server.maxplayers 100

# Team Settings
maxteamsize 4

# Performance Settings
server.saveinterval 300
fps.limit 60

# Chat and Logging
chat.serverlog true
lang en

# Gameplay Settings
instant.craft "false"

# Media Settings
BoomBox.ServerUrlList "https://www.youtube.com,https://soundcloud.com,https://open.spotify.com"

# Server Tags (helps players find your server)
server.tags "vanilla,friendly,active,weekly"

# RCON Settings (Remote Console)
rcon.port 28017
rcon.password "change_this_rcon_password"
rcon.web 1

# Additional Server Settings
server.stability true
server.radiation true
decay.scale 1.0
craft.instant false

# Anti-Cheat Settings
antihack.enabled true
antihack.reporting true
antihack.admincheat false

# Comfort Settings
comfort true

# PVP Settings (set to false for PVE server)
server.pve false

# Wipe Settings
server.wipesize 2000

# Voice Chat
voice.enabled true

# Server Browser Settings
server.secure true

# Networking
network.sendrate 20
network.updaterate 20

# Console Settings
server.tickrate 30

# Debugging (set to false for production)
debug.log false
